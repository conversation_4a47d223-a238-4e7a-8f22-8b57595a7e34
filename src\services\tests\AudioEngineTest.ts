/**
 * Test utility to measure the volume drop when using Phase Vocoder -
 * Volume drop: -8.61 dB
 * Recommended gain compensation factor: 2.69
 */
export async function testPhaseVocoderVolumeDrop(audioContext: AudioContext): Promise<number> {
  // Create a test signal - a sine wave at 440Hz
  const bufferSize = audioContext.sampleRate * 2; // 2 seconds
  const testBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const channelData = testBuffer.getChannelData(0);
  
  // Fill with a sine wave
  for (let i = 0; i < bufferSize; i++) {
    channelData[i] = Math.sin(2 * Math.PI * 440 * i / audioContext.sampleRate);
  }
  
  // Function to measure RMS power of a buffer
  const measureRMS = async (buffer: AudioBuffer, usePhaseVocoder: boolean): Promise<number> => {
    // Create an offline context for analysis
    const offlineCtx = new OfflineAudioContext(1, buffer.length, buffer.sampleRate);
    
    // Create source node
    const source = offlineCtx.createBufferSource();
    source.buffer = buffer;
    
    if (usePhaseVocoder) {
      // Add the Phase Vocoder worklet
      await offlineCtx.audioWorklet.addModule('/lib/phaze/www/phase-vocoder.js');
      
      // Create and connect Phase Vocoder
      const phaseVocoderNode = new AudioWorkletNode(offlineCtx, 'phase-vocoder-processor');
      const pitchFactorParam = phaseVocoderNode.parameters.get('pitchFactor');
      if (pitchFactorParam) {
        pitchFactorParam.value = 1.0; // No pitch change, just processing
      }
      
      source.connect(phaseVocoderNode);
      phaseVocoderNode.connect(offlineCtx.destination);
    } else {
      source.connect(offlineCtx.destination);
    }
    
    // Start the source and render
    source.start();
    const renderedBuffer = await offlineCtx.startRendering();
    
    // Calculate RMS
    const data = renderedBuffer.getChannelData(0);
    let sum = 0;
    for (let i = 0; i < data.length; i++) {
      sum += data[i] * data[i];
    }
    const rms = Math.sqrt(sum / data.length);
    return rms;
  };
  
  // Measure RMS with and without Phase Vocoder
  const rmsWithout = await measureRMS(testBuffer, false);
  const rmsWith = await measureRMS(testBuffer, true);
  
  // Calculate the difference in dB
  const dbDifference = 20 * Math.log10(rmsWith / rmsWithout);
  
  console.log(`RMS without Phase Vocoder: ${rmsWithout}`);
  console.log(`RMS with Phase Vocoder: ${rmsWith}`);
  console.log(`Difference in dB: ${dbDifference.toFixed(2)} dB`);
  
  return dbDifference;
}