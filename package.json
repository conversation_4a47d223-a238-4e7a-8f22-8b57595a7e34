{"name": "mismo.dj", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "format:check": "prettier --check .", "format:write": "prettier --write .", "preview": "vite preview", "test": "jest"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@types/react-router-dom": "^5.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dexie": "^4.0.11", "essentia.js": "^0.1.3", "framer-motion": "^12.7.4", "js-sha256": "^0.11.0", "jsmediatags": "^3.9.7", "lucide-react": "^0.488.0", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "mobx-state-tree": "^7.0.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.1.4", "@types/dexie": "^1.3.32", "@types/jest": "^29.5.14", "@types/jsmediatags": "^3.9.6", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/wavesurfer.js": "^6.0.12", "@types/wicg-file-system-access": "^2023.10.6", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-mobx": "^0.0.13", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jest": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "standardized-audio-context-mock": "^9.7.22", "tailwindcss": "^4.1.4", "ts-jest": "^29.3.4", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}