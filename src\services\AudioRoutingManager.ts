// src/services/AudioRoutingManager.ts

import { getSharedAudioContext } from './AudioManager';
import { RootStoreType } from '../stores/RootStore';

/**
 * Base class for all audio processing nodes in the routing system
 */
export abstract class RoutingAudioNode {
  protected audioContext: AudioContext;
  protected inputNode: RoutingAudioNode | null = null;
  protected outputNodes: Set<RoutingAudioNode> = new Set();
  protected webAudioNode: globalThis.AudioNode | null = null;

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext;
  }

  /**
   * Connect this node to another audio node
   */
  public connect(destination: RoutingAudioNode): void {
    this.outputNodes.add(destination);
    destination.inputNode = this;
    this.connectWebAudioNodes();
  }

  /**
   * Disconnect this node from another audio node
   */
  public disconnect(destination?: RoutingAudioNode): void {
    if (destination) {
      this.outputNodes.delete(destination);
      destination.inputNode = null;
    } else {
      // Disconnect all outputs
      this.outputNodes.forEach(node => {
        node.inputNode = null;
      });
      this.outputNodes.clear();
    }
    this.disconnectWebAudioNodes();
  }

  /**
   * Get the Web Audio API node for this audio node
   */
  public abstract getWebAudioNode(): globalThis.AudioNode;

  /**
   * Connect the underlying Web Audio API nodes
   */
  protected abstract connectWebAudioNodes(): void;

  /**
   * Disconnect the underlying Web Audio API nodes
   */
  protected abstract disconnectWebAudioNodes(): void;

  /**
   * Dispose of this node and clean up resources
   */
  public dispose(): void {
    this.disconnect();
    this.webAudioNode = null;
  }
}

/**
 * Node that wraps an audio buffer source
 */
export class DeckSourceNode extends RoutingAudioNode {
  private sourceNode: AudioBufferSourceNode | null = null;
  private audioBuffer: AudioBuffer | null = null;

  constructor(audioContext: AudioContext) {
    super(audioContext);
    this.createSource();
  }

  public setAudioBuffer(buffer: AudioBuffer): void {
    this.audioBuffer = buffer;
  }

  public createSource(): AudioBufferSourceNode {
    if (this.sourceNode) {
      console.log('DeckSourceNode: Disconnecting old source node');
      this.sourceNode.disconnect();
    }

    this.sourceNode = this.audioContext.createBufferSource();
    this.sourceNode.buffer = this.audioBuffer;
    this.webAudioNode = this.sourceNode;

    console.log('DeckSourceNode: Created new source node, reconnecting to outputs');

    // Connect to output nodes if they exist
    this.connectWebAudioNodes();

    return this.sourceNode;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.sourceNode) {
      throw new Error('Source node not created. Call createSource() first.');
    }
    return this.sourceNode;
  }

  protected connectWebAudioNodes(): void {
    if (this.sourceNode && this.outputNodes.size > 0) {
      this.outputNodes.forEach(outputNode => {
        this.sourceNode!.connect(outputNode.getWebAudioNode());
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    if (this.sourceNode) {
      this.sourceNode.disconnect();
    }
  }
}

/**
 * Node that handles time-stretching using Phase Vocoder with wet/dry bypass
 */
export class TimeStretchNode extends RoutingAudioNode {
  private phaseVocoderNode: AudioWorkletNode | null = null;
  private gainCompensationNode: GainNode | null = null;
  private bypassGainNode: GainNode | null = null;
  private wetGainNode: GainNode | null = null;
  private mixerGainNode: GainNode | null = null;
  private isBypassed: boolean = true; // Start in bypass mode
  private currentPitchFactor: number = 1.0;
  private debugAnalyzer: AnalyserNode | null = null;

  constructor(audioContext: AudioContext) {
    super(audioContext);

    // Create all nodes for wet/dry mixing
    this.bypassGainNode = this.audioContext.createGain();
    this.bypassGainNode.gain.value = 1.0; // Start with bypass enabled

    this.wetGainNode = this.audioContext.createGain();
    this.wetGainNode.gain.value = 0.0; // Start with wet disabled

    this.gainCompensationNode = this.audioContext.createGain();
    this.gainCompensationNode.gain.value = 1.0;

    this.mixerGainNode = this.audioContext.createGain();
    this.mixerGainNode.gain.value = 1.0;

    // Create debug analyzer
    this.debugAnalyzer = this.audioContext.createAnalyser();
    this.debugAnalyzer.fftSize = 256;

    try {
      this.phaseVocoderNode = new AudioWorkletNode(this.audioContext, 'phase-vocoder-processor');

      // Add debug logging to phase vocoder
      this.phaseVocoderNode.port.onmessage = (event) => {
        console.log('Phase Vocoder Message:', event.data);
      };

      // Initialize with passthrough
      const pitchFactorParam = this.phaseVocoderNode.parameters.get('pitchFactor');
      if (pitchFactorParam) {
        pitchFactorParam.value = 1.0;
        console.log('TimeStretchNode: Phase vocoder initialized with pitch factor 1.0');
      }

      // Connect wet path: phaseVocoder → gainCompensation → wetGain → mixer
      this.phaseVocoderNode.connect(this.gainCompensationNode);
      this.gainCompensationNode.connect(this.wetGainNode);
      this.wetGainNode.connect(this.mixerGainNode);

      // Connect dry path: bypass → mixer
      this.bypassGainNode.connect(this.mixerGainNode);

      // Connect debug analyzer to mixer output
      this.mixerGainNode.connect(this.debugAnalyzer);

      console.log('TimeStretchNode: Internal wet/dry paths connected');

      console.log('TimeStretchNode: Wet/dry mixing setup complete');
    } catch (error) {
      console.error('Failed to initialize Phase Vocoder:', error);
      throw error;
    }
  }

  public setPitchFactor(factor: number): void {
    this.currentPitchFactor = factor;
    console.log(`TimeStretchNode: Setting pitch factor to ${factor}, bypassed: ${this.isBypassed}`);

    // Always set the phase vocoder parameter, but control routing with wet/dry mix
    if (this.phaseVocoderNode) {
      const pitchFactorParam = this.phaseVocoderNode.parameters.get('pitchFactor');
      if (pitchFactorParam) {
        pitchFactorParam.value = factor;
        console.log(`TimeStretchNode: Phase vocoder pitch factor set to ${factor}`);
      } else {
        console.error('TimeStretchNode: pitchFactor parameter not found!');
      }

      // Set gain compensation
      if (factor === 1) {
        this.gainCompensationNode!.gain.value = 1.0;
      } else {
        this.gainCompensationNode!.gain.value = 2.69;
      }
    } else {
      console.error('TimeStretchNode: phaseVocoderNode is null!');
    }

    // Update wet/dry mix based on bypass state
    this.updateWetDryMix();
  }

  /**
   * Update wet/dry mix based on bypass state
   */
  private updateWetDryMix(): void {
    if (this.isBypassed) {
      // Bypass mode: dry signal only
      this.bypassGainNode!.gain.value = 1.0;
      this.wetGainNode!.gain.value = 0.0;
      console.log('TimeStretchNode: Routing to dry path (bypass)');
    } else {
      // Wet mode: processed signal only
      this.bypassGainNode!.gain.value = 0.0;
      this.wetGainNode!.gain.value = 1.0;
      console.log('TimeStretchNode: Routing to wet path (phase vocoder)');
    }
  }

  /**
   * Enable time stretching (Master Tempo ON) - crossfade to wet path
   */
  public enableTimeStretching(): void {
    console.log('TimeStretchNode: Enabling time stretching - crossfading to wet path');
    this.isBypassed = false;
    this.updateWetDryMix();
    this.setPitchFactor(this.currentPitchFactor);
  }

  /**
   * Disable time stretching (Master Tempo OFF) - crossfade to dry path
   */
  public disableTimeStretching(): void {
    console.log('TimeStretchNode: Disabling time stretching - crossfading to dry path');
    this.isBypassed = true;
    this.updateWetDryMix();
    this.setPitchFactor(this.currentPitchFactor);
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.mixerGainNode) {
      throw new Error('TimeStretchNode not properly initialized');
    }
    return this.mixerGainNode;
  }

  protected connectWebAudioNodes(): void {
    if (!this.inputNode) {
      console.log('TimeStretchNode: No input node, skipping connection');
      return;
    }

    console.log('TimeStretchNode: Connecting external audio routing...');

    if (this.phaseVocoderNode && this.mixerGainNode && this.bypassGainNode) {
      const inputNode = this.inputNode.getWebAudioNode();
      console.log('TimeStretchNode: Input node type:', inputNode.constructor.name);

      // Connect input to both wet and dry paths
      try {
        inputNode.connect(this.phaseVocoderNode); // Wet path
        console.log('TimeStretchNode: Input connected to phase vocoder (wet path)');

        inputNode.connect(this.bypassGainNode);   // Dry path
        console.log('TimeStretchNode: Input connected to bypass gain (dry path)');
      } catch (error) {
        console.error('TimeStretchNode: Error connecting input paths:', error);
      }

      // Connect mixer to outputs
      this.outputNodes.forEach((outputNode, index) => {
        try {
          this.mixerGainNode!.connect(outputNode.getWebAudioNode());
          console.log(`TimeStretchNode: Mixer connected to output node ${index} (${outputNode.constructor.name})`);
        } catch (error) {
          console.error(`TimeStretchNode: Error connecting to output node ${index}:`, error);
        }
      });

      console.log('TimeStretchNode: External audio routing connected - input splits to wet/dry, mixer outputs to next node');
    } else {
      console.error('TimeStretchNode: Missing required nodes for connection:', {
        phaseVocoderNode: !!this.phaseVocoderNode,
        mixerGainNode: !!this.mixerGainNode,
        bypassGainNode: !!this.bypassGainNode
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    console.log('TimeStretchNode: Disconnecting external connections only...');

    // Only disconnect external connections, preserve internal wet/dry routing
    if (this.mixerGainNode) {
      // Disconnect mixer from output nodes
      this.mixerGainNode.disconnect();
    }

    // Disconnect input connections (these will be reconnected)
    if (this.phaseVocoderNode && this.inputNode) {
      try {
        this.inputNode.getWebAudioNode().disconnect(this.phaseVocoderNode);
        console.log('TimeStretchNode: Disconnected input from phase vocoder');
      } catch (e) {
        // Connection might not exist
      }
    }

    if (this.bypassGainNode && this.inputNode) {
      try {
        this.inputNode.getWebAudioNode().disconnect(this.bypassGainNode);
        console.log('TimeStretchNode: Disconnected input from bypass gain');
      } catch (e) {
        // Connection might not exist
      }
    }

    console.log('TimeStretchNode: External disconnections complete, internal routing preserved');
  }

  public isBypassMode(): boolean {
    return this.isBypassed;
  }

  public getCurrentPitchFactor(): number {
    return this.currentPitchFactor;
  }

  /**
   * Get debug information about the current audio routing
   */
  public getDebugInfo(): any {
    const dataArray = new Float32Array(this.debugAnalyzer!.frequencyBinCount);
    this.debugAnalyzer!.getFloatFrequencyData(dataArray);

    return {
      isBypassed: this.isBypassed,
      currentPitchFactor: this.currentPitchFactor,
      bypassGain: this.bypassGainNode?.gain.value,
      wetGain: this.wetGainNode?.gain.value,
      gainCompensation: this.gainCompensationNode?.gain.value,
      hasAudioSignal: dataArray.some(value => value > -100), // Check if there's any signal above -100dB
      phaseVocoderConnected: !!this.phaseVocoderNode,
      audioGraphConnected: this.outputNodes.size > 0,
      inputNodeConnected: !!this.inputNode,
      inputNodeType: this.inputNode?.constructor.name || 'none'
    };
  }

  /**
   * Reconnect input when source node changes
   */
  public reconnectInput(): void {
    if (!this.inputNode) {
      console.log('TimeStretchNode: No input node to reconnect');
      return;
    }

    console.log('TimeStretchNode: Reconnecting input after source change...');

    const inputWebAudioNode = this.inputNode.getWebAudioNode();

    // Disconnect old input connections
    if (this.phaseVocoderNode) {
      try {
        inputWebAudioNode.disconnect(this.phaseVocoderNode);
      } catch (e) {
        // Connection might not exist
      }
    }

    if (this.bypassGainNode) {
      try {
        inputWebAudioNode.disconnect(this.bypassGainNode);
      } catch (e) {
        // Connection might not exist
      }
    }

    // Reconnect to both paths
    if (this.phaseVocoderNode && this.bypassGainNode) {
      inputWebAudioNode.connect(this.phaseVocoderNode); // Wet path
      inputWebAudioNode.connect(this.bypassGainNode);   // Dry path
      console.log('TimeStretchNode: Input reconnected to both wet and dry paths');
    }
  }

  /**
   * Test phase vocoder connection by injecting a test signal directly
   */
  public async testPhaseVocoderConnection(): Promise<void> {
    if (!this.phaseVocoderNode) {
      console.error('TimeStretchNode: No phase vocoder node to test');
      return;
    }

    console.log('TimeStretchNode: Testing phase vocoder connection...');

    // Create a test oscillator that connects directly to the phase vocoder
    const testOscillator = this.audioContext.createOscillator();
    const testGain = this.audioContext.createGain();

    testOscillator.frequency.value = 880; // A5 note
    testOscillator.type = 'sine';
    testGain.gain.value = 0.1; // Low volume

    // Connect test signal directly to phase vocoder
    testOscillator.connect(testGain);
    testGain.connect(this.phaseVocoderNode);

    // Start the test tone for 2 seconds
    testOscillator.start();
    testOscillator.stop(this.audioContext.currentTime + 2);

    console.log('TimeStretchNode: Test tone injected directly into phase vocoder');

    // Set a test pitch factor to see if it's working
    const originalFactor = this.currentPitchFactor;
    this.setPitchFactor(0.5); // Lower pitch

    setTimeout(() => {
      this.setPitchFactor(originalFactor); // Restore original
      console.log('TimeStretchNode: Phase vocoder test completed');
    }, 2500);
  }

  public dispose(): void {
    super.dispose();
    if (this.phaseVocoderNode) {
      this.phaseVocoderNode.disconnect();
      this.phaseVocoderNode = null;
    }
    if (this.gainCompensationNode) {
      this.gainCompensationNode.disconnect();
      this.gainCompensationNode = null;
    }
    if (this.bypassGainNode) {
      this.bypassGainNode.disconnect();
      this.bypassGainNode = null;
    }
    if (this.wetGainNode) {
      this.wetGainNode.disconnect();
      this.wetGainNode = null;
    }
    if (this.mixerGainNode) {
      this.mixerGainNode.disconnect();
      this.mixerGainNode = null;
    }
    if (this.debugAnalyzer) {
      this.debugAnalyzer.disconnect();
      this.debugAnalyzer = null;
    }
  }
}

/**
 * Node that handles volume control
 */
export class VolumeNode extends RoutingAudioNode {
  private gainNode: GainNode;

  constructor(audioContext: AudioContext, initialGain: number = 1.0) {
    super(audioContext);
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = initialGain;
    this.webAudioNode = this.gainNode;
  }

  public setGain(gain: number): void {
    this.gainNode.gain.value = Math.max(0, gain);
  }

  public getGain(): number {
    return this.gainNode.gain.value;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    this.outputNodes.forEach(outputNode => {
      this.gainNode.connect(outputNode.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
  }

  public dispose(): void {
    super.dispose();
    this.gainNode.disconnect();
  }
}

/**
 * Node that analyzes audio levels for visualization and auto-master selection
 */
export class AnalyzerNode extends RoutingAudioNode {
  private analyzerNode: AnalyserNode;
  private gainNode: GainNode; // Pass-through node
  private dataArray: Float32Array;
  private rmsValue: number = 0;
  private peakValue: number = 0;
  private isAnalyzing: boolean = false;
  private animationFrameId: number | null = null;

  constructor(audioContext: AudioContext, fftSize: number = 2048) {
    super(audioContext);

    this.analyzerNode = audioContext.createAnalyser();
    this.analyzerNode.fftSize = fftSize;
    this.analyzerNode.smoothingTimeConstant = 0.8;

    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1.0;

    // Connect analyzer in parallel with the main signal path
    this.gainNode.connect(this.analyzerNode);

    this.dataArray = new Float32Array(this.analyzerNode.frequencyBinCount);
    this.webAudioNode = this.gainNode;
  }

  public startAnalysis(): void {
    if (this.isAnalyzing) return;

    this.isAnalyzing = true;
    this.analyze();
  }

  public stopAnalysis(): void {
    this.isAnalyzing = false;
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  private analyze(): void {
    if (!this.isAnalyzing) return;

    this.analyzerNode.getFloatTimeDomainData(this.dataArray);

    // Calculate RMS and peak values
    let sum = 0;
    let peak = 0;

    for (let i = 0; i < this.dataArray.length; i++) {
      const sample = this.dataArray[i];
      sum += sample * sample;
      peak = Math.max(peak, Math.abs(sample));
    }

    this.rmsValue = Math.sqrt(sum / this.dataArray.length);
    this.peakValue = peak;

    this.animationFrameId = requestAnimationFrame(() => this.analyze());
  }

  public getRMS(): number {
    return this.rmsValue;
  }

  public getPeak(): number {
    return this.peakValue;
  }

  public getRMSdB(): number {
    return this.rmsValue > 0 ? 20 * Math.log10(this.rmsValue) : -Infinity;
  }

  public getPeakdB(): number {
    return this.peakValue > 0 ? 20 * Math.log10(this.peakValue) : -Infinity;
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    this.outputNodes.forEach(outputNode => {
      this.gainNode.connect(outputNode.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
    // Keep analyzer connected for monitoring
    this.gainNode.connect(this.analyzerNode);
  }

  public dispose(): void {
    super.dispose();
    this.stopAnalysis();
    this.gainNode.disconnect();
    this.analyzerNode.disconnect();
  }
}

/**
 * Node that handles EQ processing sequentially
 */
export class SequentialEQNode extends RoutingAudioNode {
  private lowEQNode: BiquadFilterNode | null = null;
  private midEQNode: BiquadFilterNode | null = null; 
  private midLoEQNode: BiquadFilterNode | null = null; 
  private midHiEQNode: BiquadFilterNode | null = null; 
  private highEQNode: BiquadFilterNode | null = null;
  private is4BandMode: boolean = false;

  constructor(audioContext: AudioContext, frequencies: {
    low: number;
    mid?: number; 
    midLo?: number; 
    midHi?: number; 
    high: number;
    is4Band?: boolean;
  }) {
    super(audioContext);
    this.is4BandMode = frequencies.is4Band || false;
    this.setupEQNodes(frequencies);
  }

  private setupEQNodes(frequencies: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
  }): void {
    // Create EQ nodes
    this.lowEQNode = this.audioContext.createBiquadFilter();
    this.lowEQNode.type = 'lowshelf';
    this.lowEQNode.frequency.value = frequencies.low;
    this.lowEQNode.Q.value = 1;
    this.lowEQNode.gain.value = 0;

    this.midEQNode = this.audioContext.createBiquadFilter();
    this.midEQNode.type = 'peaking';
    if (frequencies.mid) this.midEQNode.frequency.value = frequencies.mid;
    else
      throw(new Error('SequentialEQNode: Mid frequency not provided'));
    this.midEQNode.Q.value = 2.5;
    this.midEQNode.gain.value = 0;

    this.midLoEQNode = this.audioContext.createBiquadFilter();
    this.midLoEQNode.type = 'peaking';
    if (frequencies.midLo) this.midLoEQNode.frequency.value = frequencies.midLo;
    else
      throw(new Error('SequentialEQNode: Mid-Lo frequency not provided'));
    this.midLoEQNode.Q.value = 2.5;
    this.midLoEQNode.gain.value = 0;

    this.midHiEQNode = this.audioContext.createBiquadFilter();
    this.midHiEQNode.type = 'peaking';
    if (frequencies.midHi) this.midHiEQNode.frequency.value = frequencies.midHi;
    else
      throw(new Error('SequentialEQNode: Mid-Hi frequency not provided'));
    this.midHiEQNode.Q.value = 2.5;
    this.midHiEQNode.gain.value = 0;

    this.highEQNode = this.audioContext.createBiquadFilter();
    this.highEQNode.type = 'highshelf';
    this.highEQNode.frequency.value = frequencies.high;
    this.highEQNode.Q.value = 1;
    this.highEQNode.gain.value = 0;

    this.connectEQChain();
  }

  public setMode(is4Band: boolean): void {
    console.log(`SequentialEQNode: Setting mode to ${is4Band ? '4-band' : '3-band'}`);
    if (is4Band === this.is4BandMode) return;
    this.is4BandMode = is4Band;
    this.connectEQChain();
    console.log('SequentialEQNode: Internal connections updated for mode:', this.is4BandMode ? '4-band' : '3-band');
  }

  private connectEQChain(): void {
    // Disconnect all nodes first
    this.lowEQNode?.disconnect();
    this.midEQNode?.disconnect();
    this.midLoEQNode?.disconnect();
    this.midHiEQNode?.disconnect();
    this.highEQNode?.disconnect();

    if (this.is4BandMode) {
      // 4-band mode: low → mid-lo → mid-hi → high
      if(this.lowEQNode && this.midLoEQNode && this.midHiEQNode && this.highEQNode) {
      this.lowEQNode.connect(this.midLoEQNode);
      this.midLoEQNode.connect(this.midHiEQNode);
      this.midHiEQNode.connect(this.highEQNode);
      }
    } else {
      // 3-band mode: low → mid → high
      if(this.lowEQNode && this.midEQNode && this.highEQNode) {
        this.lowEQNode.connect(this.midEQNode);
        this.midEQNode.connect(this.highEQNode);
      }
    }
  }

  public setEQFrequencies(frequencies: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
    is4Band: boolean; 
  }): void {
    console.log('SequentialEQNode: Setting EQ frequencies:', frequencies);
    if(this.lowEQNode) {
      this.lowEQNode.frequency.value = frequencies.low;
      console.log(`SequentialEQNode: Low EQ frequency set to ${frequencies.low}`);
    }
    
    if (frequencies.is4Band) {
      if(this.midLoEQNode && frequencies.midLo) {
        this.midLoEQNode.frequency.value = frequencies.midLo;
        console.log(`SequentialEQNode: Mid-Lo EQ frequency set to ${frequencies.midLo}`);
      }
      if(this.midHiEQNode && frequencies.midHi) {
        this.midHiEQNode.frequency.value = frequencies.midHi;
        console.log(`SequentialEQNode: Mid-Hi EQ frequency set to ${frequencies.midHi}`);
      }
    } else {
      if(this.midEQNode && frequencies.mid) {
        this.midEQNode.frequency.value = frequencies.mid;
        console.log(`SequentialEQNode: Mid EQ frequency set to ${frequencies.mid}`);
      }
    }
    if(this.highEQNode) {
      this.highEQNode.frequency.value = frequencies.high;
      console.log(`SequentialEQNode: High EQ frequency set to ${frequencies.high}`);
    }
    this.setMode(frequencies.is4Band); 
  }

  public setEQ(values: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
  }, fullKill: boolean = false): void {
    console.log('SequentialEQNode: Setting EQ values:', values, 'Full Kill:', fullKill);
    const getKillGain = (val: number) => {
      if (!isFinite(val) || val <= -11.9) return -100; 
      return (val / -12) * -50;
    };

    let lowGain = values.low;
    let midGain = values.mid;
    let midLoGain = values.midLo;
    let midHiGain = values.midHi;
    let highGain = values.high;

    if (fullKill && values.low < 0) {
      lowGain = getKillGain(values.low);
    }
    if(this.lowEQNode) {
      this.lowEQNode.gain.value = lowGain;
      console.log(`SequentialEQNode: Low EQ gain set to ${lowGain}`);
    }

    if (fullKill && values.high < 0) {
      highGain = getKillGain(values.high);
    }
    if(this.highEQNode) {
      this.highEQNode.gain.value = highGain;
      console.log(`SequentialEQNode: High EQ gain set to ${highGain}`);
    }

    if (this.is4BandMode) {
      if (values.midLo !== undefined) {
        midLoGain = values.midLo;
        if (fullKill && values.midLo < 0) {
          midLoGain = getKillGain(values.midLo);
        }
        if(this.midLoEQNode) {
          this.midLoEQNode.gain.value = midLoGain;
          console.log(`SequentialEQNode: Mid-Lo EQ gain set to ${midLoGain}`);
        }
      }

      if (values.midHi !== undefined) {
        midHiGain = values.midHi;
        if (fullKill && values.midHi < 0) {
          midHiGain = getKillGain(values.midHi);
        }
        if(this.midHiEQNode) {
          this.midHiEQNode.gain.value = midHiGain;
          console.log(`SequentialEQNode: Mid-Hi EQ gain set to ${midHiGain}`);
        }
      }
    } else {
      if (values.mid !== undefined) {
        midGain = values.mid;
        if (fullKill && values.mid < 0) {
          midGain = getKillGain(values.mid);
        }
        if(this.midEQNode) {
          this.midEQNode.gain.value = midGain;
          console.log(`SequentialEQNode: Mid EQ gain set to ${midGain}`);
        }
      }
    }
  }

  public getWebAudioNode(): globalThis.AudioNode {
    if (!this.lowEQNode) {
      throw new Error('SequentialEQNode not properly initialized, lowEQNode is null');
    }
    return this.lowEQNode;
  }

  public getOutputNode(): globalThis.AudioNode {
    if (this.is4BandMode) {
      if (!this.highEQNode) throw new Error('SequentialEQNode (4-band) not properly initialized, highEQNode is null');
      return this.highEQNode;
    } else {
      if (!this.highEQNode) throw new Error('SequentialEQNode (3-band) not properly initialized, highEQNode is null');
      return this.highEQNode;
    }
  }

  protected connectWebAudioNodes(): void {
    if (this.inputNode && this.lowEQNode) {
      this.inputNode.getWebAudioNode().connect(this.lowEQNode);
    }
    const finalEQOutputNode = this.getOutputNode();
    if (finalEQOutputNode) {
      this.outputNodes.forEach(node => {
        finalEQOutputNode.connect(node.getWebAudioNode());
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    if (this.inputNode && this.lowEQNode) {
      try {
        this.inputNode.getWebAudioNode().disconnect(this.lowEQNode);
      } catch (e) { /* May already be disconnected */ }
    }
    const finalEQOutputNode = this.getOutputNode();
    if (finalEQOutputNode) {
      try {
        finalEQOutputNode.disconnect();
      } catch (e) { /* May already be disconnected */ }
    }
  }

  public dispose(): void {
    super.dispose(); 
    this.lowEQNode?.disconnect();
    this.midEQNode?.disconnect();
    this.midLoEQNode?.disconnect();
    this.midHiEQNode?.disconnect();
    this.highEQNode?.disconnect();

    this.lowEQNode = null;
    this.midEQNode = null;
    this.midLoEQNode = null;
    this.midHiEQNode = null;
    this.highEQNode = null;
  }
}

/**
 * Node that handles EQ processing in parallel
 */
export class ParallelEQNode extends RoutingAudioNode {
  private inputGain: GainNode;
  private outputSumNode: GainNode;

  // 3-band Linkwitz-Riley filters
  private lowPass1: BiquadFilterNode | null = null;
  private lowPass2: BiquadFilterNode | null = null;
  private midHighPass1: BiquadFilterNode | null = null;
  private midHighPass2: BiquadFilterNode | null = null;
  private midLowPass1: BiquadFilterNode | null = null;
  private midLowPass2: BiquadFilterNode | null = null;
  private highPass1: BiquadFilterNode | null = null;
  private highPass2: BiquadFilterNode | null = null;

  // 4-band Linkwitz-Riley filters (for 4-band mode only)
  private midLowHighPass1: BiquadFilterNode | null = null;
  private midLowHighPass2: BiquadFilterNode | null = null;
  private midLowLowPass1: BiquadFilterNode | null = null;
  private midLowLowPass2: BiquadFilterNode | null = null;
  private midHighHighPass1: BiquadFilterNode | null = null;
  private midHighHighPass2: BiquadFilterNode | null = null;
  private midHighLowPass1: BiquadFilterNode | null = null;
  private midHighLowPass2: BiquadFilterNode | null = null;

  private lowBandGain: GainNode | null = null;
  private midBandGain: GainNode | null = null;
  private highBandGain: GainNode | null = null;
  private midLoBandGain: GainNode | null = null;
  private midHiBandGain: GainNode | null = null;

  private is4BandMode: boolean = false;

  private currentFrequencies: { low: number; high: number; midCutoff?: number };

  constructor(audioContext: AudioContext, frequencies: {
    low: number;
    midLo?: number;
    midHi?: number; 
    high: number;
    is4Band?: boolean;
  }) {
    super(audioContext);
    this.inputGain = this.audioContext.createGain();
    this.outputSumNode = this.audioContext.createGain();
    this.currentFrequencies = { 
      low: frequencies.low, 
      high: frequencies.high,
      midCutoff: Math.sqrt(frequencies.midLo! * frequencies.midHi!)
    };
    this.is4BandMode = frequencies.is4Band || false;
    this.setupFiltersAndGains();
  }

  private setupFiltersAndGains(): void {
    // Disconnect and null all old nodes
    this.lowPass1?.disconnect(); this.lowPass1 = null;
    this.lowPass2?.disconnect(); this.lowPass2 = null;
    this.midHighPass1?.disconnect(); this.midHighPass1 = null;
    this.midHighPass2?.disconnect(); this.midHighPass2 = null;
    this.midLowPass1?.disconnect(); this.midLowPass1 = null;
    this.midLowPass2?.disconnect(); this.midLowPass2 = null;
    this.highPass1?.disconnect(); this.highPass1 = null;
    this.highPass2?.disconnect(); this.highPass2 = null;
    this.midLowHighPass1?.disconnect(); this.midLowHighPass1 = null;
    this.midLowHighPass2?.disconnect(); this.midLowHighPass2 = null;
    this.midLowLowPass1?.disconnect(); this.midLowLowPass1 = null;
    this.midLowLowPass2?.disconnect(); this.midLowLowPass2 = null;
    this.midHighHighPass1?.disconnect(); this.midHighHighPass1 = null;
    this.midHighHighPass2?.disconnect(); this.midHighHighPass2 = null;
    this.midHighLowPass1?.disconnect(); this.midHighLowPass1 = null;
    this.midHighLowPass2?.disconnect(); this.midHighLowPass2 = null;
    this.lowBandGain?.disconnect(); this.lowBandGain = null;
    this.midBandGain?.disconnect(); this.midBandGain = null;
    this.highBandGain?.disconnect(); this.highBandGain = null;
    this.midLoBandGain?.disconnect(); this.midLoBandGain = null;
    this.midHiBandGain?.disconnect(); this.midHiBandGain = null;
    this.inputGain.disconnect();
    this.outputSumNode.disconnect();

    const { low, high, midCutoff } = this.currentFrequencies;
    const Q_BUTTERWORTH = 0.7071067811865476;
    const ctx = this.audioContext;

    // Low band: LPF1 -> LPF2 -> lowGain -> output
    this.lowPass1 = ctx.createBiquadFilter();
    this.lowPass1.type = 'lowpass';
    this.lowPass1.frequency.value = low;
    this.lowPass1.Q.value = Q_BUTTERWORTH;
    this.lowPass2 = ctx.createBiquadFilter();
    this.lowPass2.type = 'lowpass';
    this.lowPass2.frequency.value = low;
    this.lowPass2.Q.value = Q_BUTTERWORTH;
    this.lowBandGain = ctx.createGain();

    // High band: HPF1(high) -> HPF2(high) -> highGain -> output
    this.highPass1 = ctx.createBiquadFilter();
    this.highPass1.type = 'highpass';
    this.highPass1.frequency.value = high;
    this.highPass1.Q.value = Q_BUTTERWORTH;
    this.highPass2 = ctx.createBiquadFilter();
    this.highPass2.type = 'highpass';
    this.highPass2.frequency.value = high;
    this.highPass2.Q.value = Q_BUTTERWORTH;
    this.highBandGain = ctx.createGain();

    // Connect low band
    this.inputGain.connect(this.lowPass1);
    this.lowPass1.connect(this.lowPass2);
    this.lowPass2.connect(this.lowBandGain);
    this.lowBandGain.connect(this.outputSumNode);

    // Connect mid band/s
    if(this.is4BandMode) {
      // 4-band mode
      // Mid-Lo band: HPF1(low) -> HPF2(low) -> LPF1(midCutoff) -> LPF2(midCutoff) -> midLoGain -> output
      this.midLowHighPass1 = ctx.createBiquadFilter();
      this.midLowHighPass1.type = 'highpass';
      this.midLowHighPass1.frequency.value = low;
      this.midLowHighPass1.Q.value = Q_BUTTERWORTH;
      this.midLowHighPass2 = ctx.createBiquadFilter();
      this.midLowHighPass2.type = 'highpass';
      this.midLowHighPass2.frequency.value = low;
      this.midLowHighPass2.Q.value = Q_BUTTERWORTH;
      this.midLowLowPass1 = ctx.createBiquadFilter();
      this.midLowLowPass1.type = 'lowpass';
      this.midLowLowPass1.frequency.value = midCutoff!;
      this.midLowLowPass1.Q.value = Q_BUTTERWORTH;
      this.midLowLowPass2 = ctx.createBiquadFilter();
      this.midLowLowPass2.type = 'lowpass';
      this.midLowLowPass2.frequency.value = midCutoff!;
      this.midLowLowPass2.Q.value = Q_BUTTERWORTH;
      this.midLoBandGain = ctx.createGain();

      // Mid-Hi band: HPF1(midCutoff) -> HPF2(midCutoff) -> LPF1(high) -> LPF2(high) -> midHiGain -> output
      this.midHighHighPass1 = ctx.createBiquadFilter();
      this.midHighHighPass1.type = 'highpass';
      this.midHighHighPass1.frequency.value = midCutoff!;
      this.midHighHighPass1.Q.value = Q_BUTTERWORTH;
      this.midHighHighPass2 = ctx.createBiquadFilter();
      this.midHighHighPass2.type = 'highpass';
      this.midHighHighPass2.frequency.value = midCutoff!;
      this.midHighHighPass2.Q.value = Q_BUTTERWORTH;
      this.midHighLowPass1 = ctx.createBiquadFilter();
      this.midHighLowPass1.type = 'lowpass';
      this.midHighLowPass1.frequency.value = high;
      this.midHighLowPass1.Q.value = Q_BUTTERWORTH;
      this.midHighLowPass2 = ctx.createBiquadFilter();
      this.midHighLowPass2.type = 'lowpass';
      this.midHighLowPass2.frequency.value = high;
      this.midHighLowPass2.Q.value = Q_BUTTERWORTH;
      this.midHiBandGain = ctx.createGain();

      // Connect Mid-Lo band
      this.inputGain.connect(this.midLowHighPass1);
      this.midLowHighPass1.connect(this.midLowHighPass2);
      this.midLowHighPass2.connect(this.midLowLowPass1);
      this.midLowLowPass1.connect(this.midLowLowPass2);
      this.midLowLowPass2.connect(this.midLoBandGain);
      this.midLoBandGain.connect(this.outputSumNode);

      // Connect Mid-Hi band 
      this.inputGain.connect(this.midHighHighPass1);
      this.midHighHighPass1.connect(this.midHighHighPass2);
      this.midHighHighPass2.connect(this.midHighLowPass1);
      this.midHighLowPass1.connect(this.midHighLowPass2);
      this.midHighLowPass2.connect(this.midHiBandGain);
      this.midHiBandGain.connect(this.outputSumNode);
    } else {
      // 3-band mode
      // Mid band: HPF1(low) -> HPF2(low) -> LPF1(high) -> LPF2(high) -> midGain -> output
      this.midHighPass1 = ctx.createBiquadFilter();
      this.midHighPass1.type = 'highpass';
      this.midHighPass1.frequency.value = low;
      this.midHighPass1.Q.value = Q_BUTTERWORTH;
      this.midHighPass2 = ctx.createBiquadFilter();
      this.midHighPass2.type = 'highpass';
      this.midHighPass2.frequency.value = low;
      this.midHighPass2.Q.value = Q_BUTTERWORTH;
      this.midLowPass1 = ctx.createBiquadFilter();
      this.midLowPass1.type = 'lowpass';
      this.midLowPass1.frequency.value = high;
      this.midLowPass1.Q.value = Q_BUTTERWORTH;
      this.midLowPass2 = ctx.createBiquadFilter();
      this.midLowPass2.type = 'lowpass';
      this.midLowPass2.frequency.value = high;
      this.midLowPass2.Q.value = Q_BUTTERWORTH;
      this.midBandGain = ctx.createGain();

      // Connect mid band
      this.inputGain.connect(this.midHighPass1);
      this.midHighPass1.connect(this.midHighPass2);
      this.midHighPass2.connect(this.midLowPass1);
      this.midLowPass1.connect(this.midLowPass2);
      this.midLowPass2.connect(this.midBandGain);
      this.midBandGain.connect(this.outputSumNode);
    }

    // Connect high band
    this.inputGain.connect(this.highPass1);
    this.highPass1.connect(this.highPass2);
    this.highPass2.connect(this.highBandGain);
    this.highBandGain.connect(this.outputSumNode);
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.inputGain;
  }

  public getOutputNode(): globalThis.AudioNode {
    return this.outputSumNode;
  }

  protected connectWebAudioNodes(): void {
    if (this.inputNode) {
      this.inputNode.getWebAudioNode().connect(this.inputGain);
    }
    this.outputNodes.forEach(node => {
      this.outputSumNode.connect(node.getWebAudioNode());
    });
  }

  protected disconnectWebAudioNodes(): void {
    if (this.inputNode) {
      try {
        this.inputNode.getWebAudioNode().disconnect(this.inputGain);
      } catch(e) {/* might already be disconnected */}
    }
    try {
      this.outputSumNode.disconnect(); 
    } catch(e) {/* might already be disconnected */}
  }

  public setEQ(values: {
    low: number;
    mid?: number;
    high: number;
    midLo?: number;
    midHi?: number;
  }, fullKill: boolean = false): void {
    const getKillGain = (val: number) => (!isFinite(val) || val <= -11.9) ? -100 : (val / -12) * -50;
    const gainToLinear = (gainValueDB: number) => Math.pow(10, gainValueDB / 20);
    const lowGainVal = gainToLinear(fullKill && values.low < 0 ? getKillGain(values.low) : values.low);
    const highGainVal = gainToLinear(fullKill && values.high < 0 ? getKillGain(values.high) : values.high);
    if (this.lowBandGain) this.lowBandGain.gain.value = lowGainVal;
    if (this.highBandGain) this.highBandGain.gain.value = highGainVal;

    if (this.is4BandMode) {
      if (this.midLoBandGain) this.midLoBandGain.gain.value = gainToLinear(fullKill && values.midLo !== undefined && values.midLo < 0 ? getKillGain(values.midLo) : (values.midLo ?? 0));
      if (this.midHiBandGain) this.midHiBandGain.gain.value = gainToLinear(fullKill && values.midHi !== undefined && values.midHi < 0 ? getKillGain(values.midHi) : (values.midHi ?? 0));
    } else {
      if (this.midBandGain) this.midBandGain.gain.value = gainToLinear(fullKill && values.mid !== undefined && values.mid < 0 ? getKillGain(values.mid) : (values.mid ?? 0));
    }
  }

  public setEQFrequencies(frequencies: {
    low: number;
    high: number;
    midHi?: number;
    midLo?: number;
    is4Band?: boolean;
  }): void {
    this.currentFrequencies = { low: frequencies.low, high: frequencies.high, midCutoff: Math.sqrt(frequencies.midLo! * frequencies.midHi!) };
    this.is4BandMode = frequencies.is4Band || false;
    this.setupFiltersAndGains();
  }

  public setMode(is4Band: boolean): void {
    this.is4BandMode = is4Band || false;
    this.setupFiltersAndGains();
  }

  public dispose(): void {
    super.dispose();
    this.inputGain.disconnect();
    this.outputSumNode.disconnect();
    this.lowPass1?.disconnect();
    this.lowPass2?.disconnect();
    this.midHighPass1?.disconnect();
    this.midHighPass2?.disconnect();
    this.midLowPass1?.disconnect();
    this.midLowPass2?.disconnect();
    this.midLowHighPass1?.disconnect();
    this.midLowHighPass2?.disconnect();
    this.midLowLowPass1?.disconnect();
    this.midLowLowPass2?.disconnect();
    this.midHighHighPass1?.disconnect();
    this.midHighHighPass2?.disconnect();
    this.midHighLowPass1?.disconnect();
    this.midHighLowPass2?.disconnect();
    this.highPass1?.disconnect();
    this.highPass2?.disconnect();
    this.lowBandGain?.disconnect();
    this.midBandGain?.disconnect();
    this.highBandGain?.disconnect();
    this.midLoBandGain?.disconnect();
    this.midHiBandGain?.disconnect();
    this.lowPass1 = null;
    this.lowPass2 = null;
    this.midHighPass1 = null;
    this.midHighPass2 = null;
    this.midLowPass1 = null;
    this.midLowPass2 = null;
    this.midLowHighPass1 = null;
    this.midLowHighPass2 = null;
    this.midLowLowPass1 = null;
    this.midLowLowPass2 = null;
    this.midHighHighPass1 = null;
    this.midHighHighPass2 = null;
    this.midHighLowPass1 = null;
    this.midHighLowPass2 = null;
    this.highPass1 = null;
    this.highPass2 = null;
    this.lowBandGain = null;
    this.midBandGain = null;
    this.highBandGain = null;
    this.midLoBandGain = null;
    this.midHiBandGain = null;
  }
}

/**
 * Wrapper EQNode that manages Sequential and Parallel EQ implementations
 */
export class EQNode extends RoutingAudioNode {
  public sequentialEQ: SequentialEQNode; 
  public parallelEQ: ParallelEQNode; 
  public activeEQImplementation: RoutingAudioNode | null = null; 
  private rootStore: RootStoreType;
  private initialFrequencies: any; 

  constructor(
    audioContext: AudioContext,
    frequencies: { low: number; mid?: number; midLo?: number; midHi?: number; high: number, is4Band?: boolean },
    rootStore: RootStoreType
  ) {
    super(audioContext);
    this.rootStore = rootStore;
    this.initialFrequencies = frequencies; 

    const adaptedSeqFrequencies = {
        low: frequencies.low,
        mid: frequencies.mid !== undefined ? frequencies.mid : 1000, 
        midLo: frequencies.midLo !== undefined ? frequencies.midLo : 350, 
        midHi: frequencies.midHi !== undefined ? frequencies.midHi : 2500, 
        high: frequencies.high,
        is4Band: frequencies.is4Band || false,
    };
    this.sequentialEQ = new SequentialEQNode(audioContext, adaptedSeqFrequencies);
    this.parallelEQ = new ParallelEQNode(audioContext, frequencies);

    this._updateActiveEQImplementation();
  }

  public _updateActiveEQImplementation(): void {
    const method = this.rootStore.settingsStore.eqProcessingMethod;
    console.log(`EQNode: _updateActiveEQImplementation called. Method from settings: ${method}`); 

    const newImpl = (method === 'parallel') ? this.parallelEQ : this.sequentialEQ;
    const newImplName = (method === 'parallel') ? 'ParallelEQNode' : 'SequentialEQNode';

    if (newImpl === this.activeEQImplementation) {
      console.log(`EQNode: No change in active implementation. Still ${newImplName}.`);
      return;
    }

    const oldImpl = this.activeEQImplementation;
    const oldImplName = oldImpl === this.parallelEQ ? 'ParallelEQNode' : (oldImpl === this.sequentialEQ ? 'SequentialEQNode' : 'None');
    this.activeEQImplementation = newImpl;
    console.log(`EQNode: Switching EQ implementation from ${oldImplName} to ${newImplName}`);

    if (oldImpl) {
      console.log(`EQNode: Disconnecting old implementation (${oldImplName})`);
      if (this.inputNode) {
        try {
          this.inputNode.getWebAudioNode().disconnect(oldImpl.getWebAudioNode());
          console.log(`EQNode: Disconnected old ${oldImplName} from input ${this.inputNode.constructor.name}`);
        } catch (e) { console.warn(`EQNode: Error disconnecting old ${oldImplName} from input:`, e); }
      }
      try {
        (oldImpl as (SequentialEQNode | ParallelEQNode)).getOutputNode().disconnect();
         console.log(`EQNode: Disconnected old ${oldImplName} from all its outputs.`);
      } catch (e) { console.warn(`EQNode: Error disconnecting old ${oldImplName} outputs:`, e); }
    }

    if (this.activeEQImplementation) {
      console.log(`EQNode: Connecting new implementation (${newImplName})`);
      if (this.inputNode) {
        this.inputNode.getWebAudioNode().connect(this.activeEQImplementation.getWebAudioNode());
        console.log(`EQNode: Connected new ${newImplName} to input ${this.inputNode.constructor.name}`);
      }
      this.outputNodes.forEach(outNode => {
        try {
          (this.activeEQImplementation as (SequentialEQNode | ParallelEQNode)).getOutputNode().connect(outNode.getWebAudioNode());
          console.log(`EQNode: Connected new ${newImplName} output to ${outNode.constructor.name}`);
        } catch (e) { console.warn(`EQNode: Error connecting new ${newImplName} to output ${outNode.constructor.name}:`, e); }
      });
    }
  }

  public getWebAudioNode(): globalThis.AudioNode {
    this._updateActiveEQImplementation(); 
    if (!this.activeEQImplementation) throw new Error("EQNode: No active implementation found.");
    return this.activeEQImplementation.getWebAudioNode();
  }

  public getOutputNode(): globalThis.AudioNode {
    this._updateActiveEQImplementation(); 
    if (!this.activeEQImplementation) throw new Error("EQNode: No active implementation found for output.");
    return (this.activeEQImplementation as (SequentialEQNode | ParallelEQNode)).getOutputNode();
  }

  protected connectWebAudioNodes(): void {
    this._updateActiveEQImplementation(); 
    if (this.inputNode && this.activeEQImplementation) {
      try {
        this.inputNode.getWebAudioNode().connect(this.activeEQImplementation.getWebAudioNode());
      } catch (e) { console.warn("EQNode connectWebAudioNodes: Error connecting input", e); }
    }
    if (this.activeEQImplementation) {
      this.outputNodes.forEach(outNode => {
        try {
          (this.activeEQImplementation as (SequentialEQNode | ParallelEQNode)).getOutputNode().connect(outNode.getWebAudioNode());
        } catch (e) { console.warn("EQNode connectWebAudioNodes: Error connecting output", e); }
      });
    }
  }

  protected disconnectWebAudioNodes(): void {
    if (this.inputNode && this.activeEQImplementation) {
        try {
            this.inputNode.getWebAudioNode().disconnect(this.activeEQImplementation.getWebAudioNode());
        } catch (e) { /* May already be disconnected */ }
    }
    if (this.activeEQImplementation) {
        try {
            (this.activeEQImplementation as (SequentialEQNode | ParallelEQNode)).getOutputNode().disconnect();
        } catch (e) { /* May already be disconnected */ }
    }
  }

  public setEQ(values: { low: number; mid?: number; midLo?: number; midHi?: number; high: number; }, fullKill: boolean = false): void {
    this._updateActiveEQImplementation();
    if (!this.activeEQImplementation) return;

    if (this.activeEQImplementation === this.sequentialEQ) {
      this.sequentialEQ.setEQ(values, fullKill);
    } else if (this.activeEQImplementation === this.parallelEQ) {
      this.parallelEQ.setEQ(values, fullKill);
    }
  }

  public setEQFrequencies(frequencies: { low: number; mid?: number; midLo?: number; midHi?: number; high: number; is4Band?: boolean }): void {
    const seqFreqs = {
      ...frequencies,
      mid: frequencies.mid !== undefined ? frequencies.mid : this.initialFrequencies.mid, // provide default
      midLo: frequencies.midLo !== undefined ? frequencies.midLo : this.initialFrequencies.midLo,
      midHi: frequencies.midHi !== undefined ? frequencies.midHi : this.initialFrequencies.midHi,
      is4Band: frequencies.is4Band !== undefined ? frequencies.is4Band : (frequencies.midLo !== undefined && frequencies.midHi !== undefined)
    };
    this.sequentialEQ.setEQFrequencies(seqFreqs as any); 
    this.parallelEQ.setEQFrequencies(frequencies);
  }

  public setMode(is4Band: boolean): void {
    this.sequentialEQ.setMode(is4Band);
    this.parallelEQ.setMode(is4Band); 
    this._updateActiveEQImplementation(); 
  }

  public dispose(): void {
    this.sequentialEQ.dispose();
    this.parallelEQ.dispose();
    // @ts-ignore
    this.sequentialEQ = null;
    // @ts-ignore
    this.parallelEQ = null;
    this.activeEQImplementation = null;
    super.dispose();
  }
}

/**
 * Node that handles final output routing
 */
export class OutputNode extends RoutingAudioNode {
  private gainNode: GainNode;
  private destination: globalThis.AudioNode | AudioDestinationNode;

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);
    this.gainNode = audioContext.createGain();
    this.gainNode.gain.value = 1.0;
    this.destination = destination || audioContext.destination;
    this.gainNode.connect(this.destination);
    this.webAudioNode = this.gainNode;
  }

  public setDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.gainNode.disconnect();
    this.destination = destination;
    this.gainNode.connect(this.destination);
  }

  public getWebAudioNode(): globalThis.AudioNode {
    return this.gainNode;
  }

  public getGainNode(): GainNode {
    return this.gainNode;
  }

  protected connectWebAudioNodes(): void {
    // Output node doesn't connect to other nodes in our system
    // It connects directly to the destination
  }

  protected disconnectWebAudioNodes(): void {
    this.gainNode.disconnect();
    this.gainNode.connect(this.destination);
  }

  public dispose(): void {
    super.dispose();
    this.gainNode.disconnect();
  }
}

/**
 * Represents a complete audio signal path
 */
export abstract class AudioPath {
  protected nodes: RoutingAudioNode[] = [];
  protected audioContext: AudioContext;
  protected isConnected: boolean = false;

  constructor(audioContext: AudioContext) {
    this.audioContext = audioContext;
  }

  /**
   * Connect all nodes in the path
   */
  public connect(): void {
    if (this.isConnected) {
      console.log('AudioPath: Already connected, skipping');
      return;
    }

    console.log('AudioPath: Connecting nodes...');
    for (let i = 0; i < this.nodes.length - 1; i++) {
      console.log(`AudioPath: Connecting ${this.nodes[i].constructor.name} → ${this.nodes[i + 1].constructor.name}`);
      this.nodes[i].connect(this.nodes[i + 1]);
    }
    this.isConnected = true;
    console.log('AudioPath: All nodes connected');
  }

  /**
   * Force reconnection of all nodes in the path (useful when source nodes are recreated)
   */
  public reconnect(): void {
    console.log('AudioPath: Force reconnecting all nodes...');

    // Disconnect all nodes first
    this.nodes.forEach(node => node.disconnect());

    // Reconnect them
    for (let i = 0; i < this.nodes.length - 1; i++) {
      console.log(`AudioPath: Reconnecting ${this.nodes[i].constructor.name} → ${this.nodes[i + 1].constructor.name}`);
      this.nodes[i].connect(this.nodes[i + 1]);
    }

    console.log('AudioPath: All nodes reconnected');
  }

  /**
   * Disconnect all nodes in the path
   */
  public disconnect(): void {
    if (!this.isConnected) return;

    this.nodes.forEach(node => node.disconnect());
    this.isConnected = false;
  }

  /**
   * Get a specific node by type
   */
  public getNode<T extends RoutingAudioNode>(nodeType: new (...args: any[]) => T): T | null {
    return this.nodes.find(node => node instanceof nodeType) as T || null;
  }

  /**
   * Dispose of all nodes in the path
   */
  public dispose(): void {
    this.disconnect();
    this.nodes.forEach(node => node.dispose());
    this.nodes = [];
  }
}

/**
 * Audio path for a single deck
 */
export class DeckPath extends AudioPath {
  private sourceNode: DeckSourceNode;
  private timeStretchNode: TimeStretchNode;
  private eqNode: EQNode; 
  private volumeNode: VolumeNode;
  private crossfaderNode: VolumeNode;
  private analyzerNode: AnalyzerNode;
  private rootStore: RootStoreType; 

  constructor(
    audioContext: AudioContext,
    eqFrequencies: {
      low: number;
      mid: number; // Keep this for compatibility, EQNode constructor adapts it
      midLo: number;
      midHi: number;
      high: number;
    },
    rootStore: RootStoreType 
  ) {
    super(audioContext);
    this.rootStore = rootStore; 

    this.sourceNode = new DeckSourceNode(audioContext);
    this.timeStretchNode = new TimeStretchNode(audioContext);
    // Pass all potential frequency points; EQNode will adapt
    this.eqNode = new EQNode(audioContext, eqFrequencies, this.rootStore); 
    this.volumeNode = new VolumeNode(audioContext, 0.7);
    this.crossfaderNode = new VolumeNode(audioContext, 1.0);
    this.analyzerNode = new AnalyzerNode(audioContext);

    this.nodes = [
      this.sourceNode,
      this.timeStretchNode,
      this.eqNode,
      this.volumeNode,
      this.crossfaderNode,
      this.analyzerNode
    ];
  }

  public enableMasterTempo(playbackRate: number): void {
    console.log(`DeckPath: Enabling master tempo with rate ${playbackRate}`);
    this.timeStretchNode.setPitchFactor(1.0 / playbackRate);
    this.timeStretchNode.enableTimeStretching();
    
  }

  public disableMasterTempo(): void {
    console.log('DeckPath: Disabling master tempo');
    this.timeStretchNode.disableTimeStretching();
    this.timeStretchNode.setPitchFactor(1.0); 
  }

  public getTimeStretchNode(): TimeStretchNode {
    return this.timeStretchNode;
  }

  public setAudioBuffer(buffer: AudioBuffer): void {
    this.sourceNode.setAudioBuffer(buffer);
  }

  public createSource(): AudioBufferSourceNode {
    const newSource = this.sourceNode.createSource();
    console.log('DeckPath: New source created, reconnecting TimeStretchNode input...');
    this.timeStretchNode.reconnectInput();
    return newSource;
  }

  public setVolume(volume: number): void {
    this.volumeNode.setGain(volume);
  }

  public setCrossfaderGain(gain: number): void {
    this.crossfaderNode.setGain(gain);
  }

  public setEQ(values: {
    low: number;
    mid?: number;
    midLo?: number;
    midHi?: number;
    high: number;
  }, fullKill: boolean = false): void {
    this.eqNode.setEQ(values, fullKill);
  }

  public setEQFrequencies(frequencies: {
    low: number;
    mid: number; // Keep for consistency with older calls if any, EQNode adapts
    midLo: number;
    midHi: number;
    high: number;
    is4Band: boolean;
  }): void {
    this.eqNode.setEQFrequencies(frequencies);
  }

  public setEQMode(is4Band: boolean): void {
    this.eqNode.setMode(is4Band);
  }

  public getAnalyzer(): AnalyzerNode {
    return this.analyzerNode;
  }

  public getSourceNode(): DeckSourceNode {
    return this.sourceNode;
  }

  public getOutputNode(): RoutingAudioNode {
    return this.analyzerNode;
  }

  public getAudioRoutingDebugInfo(): any {
    const timeStretchDebug = this.timeStretchNode.getDebugInfo();
    return {
      deckPath: {
        isConnected: this.isConnected,
        nodeCount: this.nodes.length,
        nodes: this.nodes.map(node => node.constructor.name)
      },
      timeStretch: timeStretchDebug,
      volume: {
        deckVolume: this.volumeNode.getGain(),
        crossfaderGain: this.crossfaderNode.getGain()
      },
      analyzer: {
        rms: this.analyzerNode.getRMS(),
        peak: this.analyzerNode.getPeak(),
        rmsDB: this.analyzerNode.getRMSdB(),
        peakDB: this.analyzerNode.getPeakdB()
      }
    };
  }

  public async testAudioRouting(): Promise<void> {
    console.log('DeckPath: Starting audio routing test...');
    const testOscillator = this.audioContext.createOscillator();
    const testGain = this.audioContext.createGain();
    testOscillator.frequency.value = 440; 
    testOscillator.type = 'sine';
    testGain.gain.value = 0.1; 
    testOscillator.connect(testGain);
    testGain.connect(this.timeStretchNode.getWebAudioNode());
    testOscillator.start();
    testOscillator.stop(this.audioContext.currentTime + 1);
    console.log('DeckPath: Test tone injected into time stretch node');
    setTimeout(() => {
      const debugInfo = this.getAudioRoutingDebugInfo();
      console.log('DeckPath: Audio routing debug info:', debugInfo);
    }, 500);
  }

  public async testPhaseVocoder(): Promise<void> {
    console.log('DeckPath: Testing phase vocoder directly...');
    await this.timeStretchNode.testPhaseVocoderConnection();
  }

  public reconnectTimeStretch(): void {
    console.log('DeckPath: Force reconnecting TimeStretchNode input...');
    this.timeStretchNode.reconnectInput();
  }

  public reconfigureEQNode(): void {
    console.log('DeckPath: reconfigureEQNode called.');
    if (this.eqNode) {
      this.eqNode._updateActiveEQImplementation();
    }
  }
}

/**
 * Audio path for master output
 */
export class MasterPath extends AudioPath {
  private inputMixer: VolumeNode;
  private masterVolumeNode: VolumeNode;
  private masterAnalyzer: AnalyzerNode;
  private outputNode: OutputNode;

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);
    this.inputMixer = new VolumeNode(audioContext, 1.0);
    this.masterVolumeNode = new VolumeNode(audioContext, 0.7);
    this.masterAnalyzer = new AnalyzerNode(audioContext);
    this.outputNode = new OutputNode(audioContext, destination);
    this.nodes = [
      this.inputMixer,
      this.masterVolumeNode,
      this.masterAnalyzer,
      this.outputNode
    ];
  }

  public connect(): void {
    console.log('MasterPath: Connecting nodes...');
    if (this.isConnected) {
      console.log('MasterPath: Already connected');
      return;
    }
    try {
      for (let i = 0; i < this.nodes.length - 1; i++) {
        this.nodes[i].connect(this.nodes[i + 1]);
        console.log(`MasterPath: Connected node ${i} to node ${i + 1}`);
      }
      const outputNode = this.getOutputNode();
      outputNode.setDestination(this.audioContext.destination);
      console.log('MasterPath: Output node connected to destination');
      this.isConnected = true;
      console.log('MasterPath: All nodes connected successfully');
    } catch (error) {
      console.error('MasterPath: Error connecting nodes:', error);
      throw error;
    }
  }

  public getInputNode(): VolumeNode {
    return this.inputMixer;
  }

  public setMasterVolume(volume: number): void {
    this.masterVolumeNode.setGain(volume);
  }

  public getMasterVolume(): number {
    return this.masterVolumeNode.getGain();
  }

  public getMasterAnalyzer(): AnalyzerNode {
    return this.masterAnalyzer;
  }

  public setOutputDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.outputNode.setDestination(destination);
  }

  public getNodeCount(): number {
    return this.nodes.length;
  }

  public isPathConnected(): boolean {
    return this.isConnected;
  }

  public getOutputNode(): OutputNode {
    return this.outputNode;
  }
}

/**
 * Audio path for headphone monitoring
 */
export class HeadphonePath extends AudioPath {
  private inputMixer: VolumeNode;
  private headphoneVolumeNode: VolumeNode;
  private outputNode: OutputNode;
  private connectedDecks: Set<string> = new Set();

  constructor(audioContext: AudioContext, destination?: globalThis.AudioNode | AudioDestinationNode) {
    super(audioContext);
    this.inputMixer = new VolumeNode(audioContext, 1.0);
    this.headphoneVolumeNode = new VolumeNode(audioContext, 0.5);
    this.outputNode = new OutputNode(audioContext, destination);
    this.nodes = [
      this.inputMixer,
      this.headphoneVolumeNode,
      this.outputNode
    ];
  }

  public getInputNode(): VolumeNode {
    return this.inputMixer;
  }

  public setHeadphoneVolume(volume: number): void {
    this.headphoneVolumeNode.setGain(volume);
  }

  public addDeck(deckId: string): void {
    this.connectedDecks.add(deckId);
  }

  public removeDeck(deckId: string): void {
    this.connectedDecks.delete(deckId);
  }

  public getConnectedDecks(): Set<string> {
    return new Set(this.connectedDecks);
  }

  public setOutputDestination(destination: globalThis.AudioNode | AudioDestinationNode): void {
    this.outputNode.setDestination(destination);
  }
}

/**
 * Central manager for all audio routing in the DJ application
 */
export class AudioRoutingManager {
  private audioContext: AudioContext;
  private rootStore: RootStoreType;
  private deckPaths: Map<string, DeckPath> = new Map();
  private masterPath: MasterPath;
  private headphonePath: HeadphonePath;
  private isInitialized: boolean = false;

  constructor(rootStore: RootStoreType) {
    this.rootStore = rootStore;
    this.audioContext = getSharedAudioContext();
    this.masterPath = new MasterPath(this.audioContext);
    this.headphonePath = new HeadphonePath(this.audioContext);
  }

  public async initialize(): Promise<void> {
    console.log('AudioRoutingManager: Initializing...');
    if (this.isInitialized) return;
    try {
      if (this.audioContext.state === 'suspended') {
        console.log('AudioRoutingManager: Resuming suspended audio context...');
        await this.audioContext.resume();
        console.log(`AudioRoutingManager: Audio context state: ${this.audioContext.state}`);
      }
      this.ensureMasterPathOutput();
      this.masterPath.connect();
      console.log('AudioRoutingManager: Master path connected');
      this.headphonePath.connect();
      console.log('AudioRoutingManager: Headphone path connected');
      this.startAnalyzers();
      console.log('AudioRoutingManager: Analyzers started');
      this.isInitialized = true;
      console.log('AudioRoutingManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AudioRoutingManager:', error);
      throw error;
    }
  }

  public async createDeckPath(deckId: string): Promise<DeckPath> {
    console.log(`Creating deck path for deck ${deckId}`);
    const eqFrequencies = {
      low: this.rootStore.settingsStore.lowEQFrequency,
      mid: this.rootStore.settingsStore.midEQFrequency,
      midLo: this.rootStore.settingsStore.midLoEQFrequency,
      midHi: this.rootStore.settingsStore.midHiEQFrequency,
      high: this.rootStore.settingsStore.highEQFrequency
    };
    const deckPath = new DeckPath(this.audioContext, eqFrequencies, this.rootStore);
    deckPath.setEQMode(this.rootStore.settingsStore.eqBands === "4-band");
    deckPath.connect();
    console.log(`AudioRoutingManager: Deck path ${deckId} internal connections made`);
    deckPath.getAnalyzer().startAnalysis();
    console.log(`AudioRoutingManager: Deck ${deckId} analyzer started`);
    deckPath.getOutputNode().getWebAudioNode().connect(this.masterPath.getInputNode().getWebAudioNode());
    console.log(`AudioRoutingManager: Deck ${deckId} connected to master input`);
    this.deckPaths.set(deckId, deckPath);
    console.log(`Created deck path for deck ${deckId}`);
    return deckPath;
  }

  public async getDeckPath(deckId: string): Promise<DeckPath | null> {
    console.log(`Getting deck path for deck ${deckId}`, this.deckPaths.has(deckId));
    if (this.deckPaths.has(deckId)) {
      return this.deckPaths.get(deckId) || null;
    } else {
      await this.initialize();
      const deckPath = await this.createDeckPath(deckId);
      return deckPath;
    }
  }

  public getMasterPath(): MasterPath {
    return this.masterPath;
  }

  public getHeadphonePath(): HeadphonePath {
    return this.headphonePath;
  }

  public enableHeadphoneMonitoring(deckId: string): void {
    const deckPath = this.deckPaths.get(deckId);
    if (!deckPath) {
      console.warn(`Deck path not found for deck ${deckId}`);
      return;
    }
    deckPath.getOutputNode().getWebAudioNode().connect(this.headphonePath.getInputNode().getWebAudioNode());
    this.headphonePath.addDeck(deckId);
    console.log(`Enabled headphone monitoring for deck ${deckId}`);
  }

  public disableHeadphoneMonitoring(deckId: string): void {
    const deckPath = this.deckPaths.get(deckId);
    if (!deckPath) {
      console.warn(`Deck path not found for deck ${deckId}`);
      return;
    }
    deckPath.getOutputNode().getWebAudioNode().disconnect(this.headphonePath.getInputNode().getWebAudioNode());
    this.headphonePath.removeDeck(deckId);
    console.log(`Disabled headphone monitoring for deck ${deckId}`);
  }

  public enableMasterHeadphoneMonitoring(): void {
    this.masterPath.getMasterAnalyzer().getWebAudioNode().connect(this.headphonePath.getInputNode().getWebAudioNode());
    console.log('Enabled master headphone monitoring');
  }

  public disableMasterHeadphoneMonitoring(): void {
    this.masterPath.getMasterAnalyzer().getWebAudioNode().disconnect(this.headphonePath.getInputNode().getWebAudioNode());
    console.log('Disabled master headphone monitoring');
  }

  public async setOutputDevice(deviceId: string, type: 'master' | 'headphone'): Promise<void> {
    try {
      console.log(`Setting ${type} output device to: ${deviceId}`);
    } catch (error) {
      console.error(`Failed to set ${type} output device:`, error);
      throw error;
    }
  }

  public ensureMasterPathOutput(): void {
    const outputNode = this.masterPath.getOutputNode();
    if (outputNode) {
      outputNode.disconnect();
      outputNode.setDestination(this.audioContext.destination);
      console.log('AudioRoutingManager: Master output node reconnected to destination');
    }
  }

  private startAnalyzers(): void {
    this.deckPaths.forEach((deckPath) => {
      const analyzer = deckPath.getAnalyzer();
      analyzer.startAnalysis();
    });
    this.masterPath.getMasterAnalyzer().startAnalysis();
  }

  public updateEQFrequencies(): void {
    console.log('AudioRoutingManager: Updating EQ frequencies for all decks');
    console.log('EQ Frequencies:', {
      low: this.rootStore.settingsStore.lowEQFrequency,
      mid: this.rootStore.settingsStore.midEQFrequency,
      midLo: this.rootStore.settingsStore.midLoEQFrequency,
      midHi: this.rootStore.settingsStore.midHiEQFrequency,
      high: this.rootStore.settingsStore.highEQFrequency,
      is4Band: this.rootStore.settingsStore.eqBands === "4-band"
    });
    this.deckPaths.forEach((deckPath, id) => {
      console.log(`Updating EQ frequencies for deck ${id}`);
      const frequencies = {
        low: this.rootStore.settingsStore.lowEQFrequency,
        mid: this.rootStore.settingsStore.midEQFrequency,
        midLo: this.rootStore.settingsStore.midLoEQFrequency,
        midHi: this.rootStore.settingsStore.midHiEQFrequency,
        high: this.rootStore.settingsStore.highEQFrequency,
        is4Band: this.rootStore.settingsStore.eqBands === "4-band"
      };
      deckPath.setEQFrequencies(frequencies);
    });
  }

  public updateEQMode(): void {
    const is4Band = this.rootStore.settingsStore.eqBands === "4-band";
    this.deckPaths.forEach(deckPath => {
      deckPath.setEQMode(is4Band);
    });
    console.log(`Updated EQ mode to ${is4Band ? '4-band' : '3-band'} for all decks`);
  }

  public updateEQProcessingMethod(): void {
    console.log(`AudioRoutingManager: updateEQProcessingMethod called. Processing for ${this.deckPaths.size} deck paths.`);
    this.deckPaths.forEach((deckPath, deckId) => {
      console.log(`AudioRoutingManager: Calling reconfigureEQNode for deck ${deckId}`);
      if (deckPath && typeof deckPath.reconfigureEQNode === 'function') {
        deckPath.reconfigureEQNode();
      }
    });
    console.log(`AudioRoutingManager: Finished updating EQ processing method to ${this.rootStore.settingsStore.eqProcessingMethod} for all decks`);
  }

  public getCompleteAudioRoutingDebugInfo(): any {
    const deckDebugInfo: any = {};
    this.deckPaths.forEach((deckPath, deckId) => {
      deckDebugInfo[deckId] = deckPath.getAudioRoutingDebugInfo();
    });
    return {
      isInitialized: this.isInitialized,
      audioContextState: this.audioContext.state,
      audioContextSampleRate: this.audioContext.sampleRate,
      deckCount: this.deckPaths.size,
      decks: deckDebugInfo,
      masterPath: {
        isConnected: this.masterPath.isPathConnected(),
        nodeCount: this.masterPath.getNodeCount(),
        masterVolume: this.masterPath.getMasterVolume(),
        masterAnalyzer: {
          rms: this.masterPath.getMasterAnalyzer().getRMS(),
          peak: this.masterPath.getMasterAnalyzer().getPeak()
        }
      }
    };
  }

  public async testDeckAudioRouting(deckId: string): Promise<void> {
    const deckPath = this.deckPaths.get(deckId);
    if (!deckPath) {
      console.error(`No deck path found for deck ${deckId}`);
      return;
    }
    console.log(`Testing audio routing for deck ${deckId}...`);
    await deckPath.testAudioRouting();
    setTimeout(() => {
      const debugInfo = this.getCompleteAudioRoutingDebugInfo();
      console.log('Complete Audio Routing Debug Info:', debugInfo);
    }, 1000);
  }

  public visualizeAudioGraph(): void {
    console.log('=== AUDIO GRAPH VISUALIZATION ===');
    this.deckPaths.forEach((deckPath, deckId) => {
      console.log(`\nDeck ${deckId} Audio Path:`);
      console.log('  Source → TimeStretch → EQ → Volume → Crossfader → Analyzer');
      const timeStretchDebug = deckPath.getTimeStretchNode().getDebugInfo();
      console.log(`  TimeStretch: ${timeStretchDebug.isBypassed ? 'BYPASS' : 'WET'} mode`);
      console.log(`  Pitch Factor: ${timeStretchDebug.currentPitchFactor}`);
      console.log(`  Bypass Gain: ${timeStretchDebug.bypassGain}`);
      console.log(`  Wet Gain: ${timeStretchDebug.wetGain}`);
      console.log(`  Has Audio Signal: ${timeStretchDebug.hasAudioSignal}`);
    });
    console.log('\nMaster Path:');
    console.log('  InputMixer → MasterVolume → MasterAnalyzer → Output');
    console.log(`  Connected: ${this.masterPath.isPathConnected()}`);
    console.log(`  Master Volume: ${this.masterPath.getMasterVolume()}`);
    console.log('\n=== END AUDIO GRAPH ===');
  }

  public dispose(): void {
    console.log('Disposing AudioRoutingManager...');
    this.deckPaths.forEach(deckPath => {
      deckPath.getAnalyzer().stopAnalysis();
    });
    this.masterPath.getMasterAnalyzer().stopAnalysis();
    this.deckPaths.forEach(deckPath => deckPath.dispose());
    this.deckPaths.clear();
    this.masterPath.dispose();
    this.headphonePath.dispose();
    this.isInitialized = false;
    console.log('AudioRoutingManager disposed');
  }
}