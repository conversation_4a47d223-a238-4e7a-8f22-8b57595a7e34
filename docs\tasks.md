## tasks
- [ ] create and organize AllDecksStore - should include - 
  - [ ] numberOfDecks
  - [ ] activeDeck
  - [ ] decks object
  - [ ] cross faders
  - [ ] master volume
  - [ ] headphone volume
  - [ ] headphone monitoring
  - [ ] recording
  - [ ] sync - everything from syncStore

- [ ] Isolate AudioRoutingManager
    - [ ] does not need to know about anything outside of it - everything should be passed as parameters, especially the structure of the settingsStore. This will allow the manager to be used in other contexts, such as the audio diagnostic page.
    
- [ ] verify all settings global, controller and per deck are saved to DB and loaded on startup

- [ ] auto master deck 
- [ ] sync all other decks to master

- [ ] ghost deck 
    - [ ] preserve bpm
    - [ ] preserve downbeat timing/phase alignment
    - [ ] allow other decks to continue syncing to the previous master's tempo and phase
    - [ ] have settings that decides in case no track is playing and a ghost master is used, if the ghost master should be used for sync or if sync should be disabled
    - [ ] have a STOP button that stops the ghost master, so a new track starts in its own BPM and play timing, and sync will sync to the new track
