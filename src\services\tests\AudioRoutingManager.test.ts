import { AudioContext } from 'standardized-audio-context-mock';
import { EQNode, SequentialEQNode, ParallelEQNode } from '../AudioRoutingManager';
import { SettingsStoreModel, EQProcessingMethod } from '../../stores/SettingsStore'; // Adjust path as needed
import { RootStoreModel } from '../../stores/RootStore'; // Adjust path as needed for RootStoreModel if it's simple enough to mock or use a minimal version

// Mock RootStore and SettingsStore for testing purposes
// This might need adjustment based on the actual structure and complexity of your stores.
// A simpler approach might be to mock the specific parts of the store that EQNode interacts with.

// Mock the getSharedAudioContext if it's used internally by nodes in a way that needs mocking
jest.mock('./AudioManager', () => ({
  getSharedAudioContext: jest.fn(() => new AudioContext()),
}));

describe('EQNode Wrapper', () => {
  let audioContext: AudioContext;
  let mockSettingsStore: any; // Use 'any' for simplicity or create a minimal mock type
  let mockRootStore: any;

  const initialFrequencies = {
    low: 200,
    mid: 1000, // For 3-band
    midLo: 350, // For 4-band
    midHi: 2500, // For 4-band
    high: 6500,
    is4Band: false, // Default to 3-band for initial setup in tests
  };

  beforeEach(() => {
    audioContext = new AudioContext();

    // Simple mock for SettingsStore
    mockSettingsStore = {
      eqProcessingMethod: 'sequential' as EQProcessingMethod, // Default
      // Mock any other settings EQNode might read, though for this test, eqProcessingMethod is key
    };

    // Simple mock for RootStore
    mockRootStore = {
      settingsStore: mockSettingsStore,
      // Mock other parts of RootStore if EQNode constructor or methods access them
    };
  });

  test('should instantiate SequentialEQNode by default', () => {
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    expect(eqNode.activeEQImplementation).toBeInstanceOf(SequentialEQNode);
  });

  test('should switch to ParallelEQNode when eqProcessingMethod changes to "parallel"', () => {
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    expect(eqNode.activeEQImplementation).toBeInstanceOf(SequentialEQNode);

    mockSettingsStore.eqProcessingMethod = 'parallel';
    // Manually trigger the update, as there's no automatic reaction in this mocked environment
    // In a real scenario, MobX would trigger reactions. Here, we call a method that would be involved.
    // One way is to call a method on eqNode that internally calls _updateActiveEQImplementation()
    // For example, calling getWebAudioNode() or setEQ() would trigger it.
    eqNode.getWebAudioNode(); // This should trigger _updateActiveEQImplementation

    expect(eqNode.activeEQImplementation).toBeInstanceOf(ParallelEQNode);
  });

  test('should switch back to SequentialEQNode when eqProcessingMethod changes back to "sequential"', () => {
    mockSettingsStore.eqProcessingMethod = 'parallel'; // Start with parallel
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    eqNode.getWebAudioNode(); // Initial update
    expect(eqNode.activeEQImplementation).toBeInstanceOf(ParallelEQNode);

    mockSettingsStore.eqProcessingMethod = 'sequential';
    eqNode.getWebAudioNode(); // Trigger update again

    expect(eqNode.activeEQImplementation).toBeInstanceOf(SequentialEQNode);
  });

  test('setEQ should be delegated to the active EQ implementation', () => {
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    const sequentialSpy = jest.spyOn(eqNode.sequentialEQ, 'setEQ');
    const parallelSpy = jest.spyOn(eqNode.parallelEQ, 'setEQ');

    const eqValues = { low: -6, mid: 0, high: 6 };

    // Test sequential
    mockSettingsStore.eqProcessingMethod = 'sequential';
    eqNode.setEQ(eqValues, false);
    expect(sequentialSpy).toHaveBeenCalledWith(eqValues, false);
    expect(parallelSpy).not.toHaveBeenCalled();
    sequentialSpy.mockClear();

    // Test parallel
    mockSettingsStore.eqProcessingMethod = 'parallel';
    eqNode.setEQ(eqValues, true);
    expect(parallelSpy).toHaveBeenCalledWith(eqValues, true);
    expect(sequentialSpy).not.toHaveBeenCalled();
    parallelSpy.mockClear();
  });

  test('setEQFrequencies should be delegated to both implementations and update active', () => {
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    const sequentialSpy = jest.spyOn(eqNode.sequentialEQ, 'setEQFrequencies');
    const parallelSpy = jest.spyOn(eqNode.parallelEQ, 'setEQFrequencies');

    const newFrequencies = { ...initialFrequencies, low: 250, high: 7000, is4Band: false };
    eqNode.setEQFrequencies(newFrequencies);

    expect(sequentialSpy).toHaveBeenCalledWith(expect.objectContaining(newFrequencies));
    expect(parallelSpy).toHaveBeenCalledWith(newFrequencies);
    // Also check if _updateActiveEQImplementation was triggered (e.g. by activeEQImplementation being the correct type)
    expect(eqNode.activeEQImplementation).toBeInstanceOf(SequentialEQNode); // Assuming default is sequential
  });

  test('setMode should be delegated to both implementations and update active', () => {
    const eqNode = new EQNode(audioContext as any, initialFrequencies, mockRootStore);
    const sequentialSpy = jest.spyOn(eqNode.sequentialEQ, 'setMode');
    const parallelSpy = jest.spyOn(eqNode.parallelEQ, 'setMode');

    // Initial mode is 3-band (is4Band: false in initialFrequencies)
    // Switch to 4-band
    mockSettingsStore.eqProcessingMethod = 'sequential'; // Keep it sequential for this part
    eqNode.setMode(true); // Set to 4-band
    expect(sequentialSpy).toHaveBeenCalledWith(true);
    expect(parallelSpy).toHaveBeenCalledWith(true); // Both should be updated
    expect(eqNode.activeEQImplementation).toBeInstanceOf(SequentialEQNode); // Still sequential
    // Verify internal state of active EQ if possible, or rely on its own tests

    sequentialSpy.mockClear();
    parallelSpy.mockClear();

    // Switch to parallel and change mode
    mockSettingsStore.eqProcessingMethod = 'parallel';
    eqNode.setMode(false); // Set to 3-band while parallel is active
    expect(sequentialSpy).toHaveBeenCalledWith(false);
    expect(parallelSpy).toHaveBeenCalledWith(false);
    expect(eqNode.activeEQImplementation).toBeInstanceOf(ParallelEQNode);
  });

  // TODO: Add tests for connectWebAudioNodes and disconnectWebAudioNodes to ensure they
  // correctly manage connections to the active EQ implementation's input/output.
  // This will involve mocking inputNode and outputNodes and their getWebAudioNode methods.
});

// Note: You might need to export some types/classes from AudioRoutingManager.ts if they are not already,
// or adjust import paths.
// You will also need to install Jest and standardized-audio-context-mock:
// npm install --save-dev jest @types/jest ts-jest standardized-audio-context-mock
// and configure Jest (e.g., jest.config.js and package.json script).

describe('SequentialEQNode', () => {
  let audioContext: AudioContext;
  // Adjusted for SequentialEQNode's constructor logic:
  // For 3-band, midLo and midHi should be undefined. 'is4Band' in this object is for test clarity, not direct constructor use.
  const initialFrequencies3Band = { 
    low: 200, mid: 1000, high: 6500, is4Band: false 
  }; 
  const initialFrequencies4Band = { // For 4-band, midLo and midHi are defined. 'mid' can be omitted or ignored.
    low: 200, midLo: 350, midHi: 2500, high: 6500, is4Band: true 
  };

  beforeEach(() => {
    audioContext = new AudioContext();
    // Ensure private properties are accessible for testing (they were made public in a previous step)
    // This is just a comment to acknowledge the test design.
  });

  test('should initialize filters in 3-band mode correctly', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies3Band);
    // @ts-ignore // Accessing private members for test verification
    expect(node.lowEQNode).not.toBeNull();
    // @ts-ignore
    expect(node.midEQNode).not.toBeNull();
    // @ts-ignore
    expect(node.highEQNode).not.toBeNull();
    // @ts-ignore
    expect(node.midLoEQNode).not.toBeNull(); // midLo/Hi are created but not connected in 3-band
    // @ts-ignore
    expect(node.midHiEQNode).not.toBeNull(); // midLo/Hi are created but not connected in 3-band
    // @ts-ignore
    expect(node.is4BandMode).toBe(false);

    // @ts-ignore
    expect(node.lowEQNode!.frequency.value).toBe(initialFrequencies3Band.low);
    // @ts-ignore
    expect(node.midEQNode!.frequency.value).toBe(initialFrequencies3Band.mid);
    // @ts-ignore
    expect(node.highEQNode!.frequency.value).toBe(initialFrequencies3Band.high);
  });

  test('should initialize filters in 4-band mode correctly', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies4Band);
     // @ts-ignore
    expect(node.is4BandMode).toBe(true);
    // @ts-ignore
    expect(node.lowEQNode).not.toBeNull();
     // @ts-ignore
    expect(node.midEQNode).not.toBeNull(); // midEQNode is always created
    // @ts-ignore
    expect(node.highEQNode).not.toBeNull();
    // @ts-ignore
    expect(node.midLoEQNode).not.toBeNull();
    // @ts-ignore
    expect(node.midHiEQNode).not.toBeNull();

    // @ts-ignore
    expect(node.lowEQNode!.frequency.value).toBe(initialFrequencies4Band.low);
    // @ts-ignore
    expect(node.midLoEQNode!.frequency.value).toBe(initialFrequencies4Band.midLo);
    // @ts-ignore
    expect(node.midHiEQNode!.frequency.value).toBe(initialFrequencies4Band.midHi);
    // @ts-ignore
    expect(node.highEQNode!.frequency.value).toBe(initialFrequencies4Band.high);
  });

  test('setEQ should apply gains correctly in 3-band mode', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies3Band);
    node.setMode(false); // Ensure 3-band
    const eqValues = { low: -6, mid: 3, high: -12 };
    node.setEQ(eqValues, false);
    // @ts-ignore
    expect(node.lowEQNode!.gain.value).toBeCloseTo(-6);
    // @ts-ignore
    expect(node.midEQNode!.gain.value).toBeCloseTo(3);
    // @ts-ignore
    expect(node.highEQNode!.gain.value).toBeCloseTo(-12);
  });

  test('setEQ should apply gains with fullKill in 4-band mode', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies4Band);
    node.setMode(true); // Ensure 4-band
    const eqValues = { low: -20, midLo: 5, midHi: -20, high: 0 }; 
    node.setEQ(eqValues, true); 
    // @ts-ignore
    expect(node.lowEQNode!.gain.value).toBeLessThanOrEqual(-40); 
    // @ts-ignore
    expect(node.midLoEQNode!.gain.value).toBeCloseTo(5);
    // @ts-ignore
    expect(node.midHiEQNode!.gain.value).toBeLessThanOrEqual(-40);
    // @ts-ignore
    expect(node.highEQNode!.gain.value).toBeCloseTo(0);
  });

  test('setEQFrequencies should update filter frequencies', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies3Band);
    const newFreq = { low: 180, mid: 900, midLo: 300, midHi: 2200, high: 6000, is4Band: false }; // is4Band will call setMode
    node.setEQFrequencies(newFreq);
    // @ts-ignore
    expect(node.lowEQNode!.frequency.value).toBe(180);
    // @ts-ignore
    expect(node.midEQNode!.frequency.value).toBe(900);
    // @ts-ignore
    expect(node.highEQNode!.frequency.value).toBe(6000);
  });

  test('setMode should switch between 3-band and 4-band and reconnect', () => {
    const node = new SequentialEQNode(audioContext as any, initialFrequencies3Band); 
    // @ts-ignore
    expect(node.is4BandMode).toBe(false);
    // @ts-ignore
    expect(node.getOutputNode()).toBe(node.highEQNode);

    node.setMode(true); // Switch to 4-band
    // @ts-ignore
    expect(node.is4BandMode).toBe(true);
    // @ts-ignore
    expect(node.getOutputNode()).toBe(node.highEQNode); 

    node.setMode(false); // Switch back to 3-band
    // @ts-ignore
    expect(node.is4BandMode).toBe(false);
    // @ts-ignore
    expect(node.getOutputNode()).toBe(node.highEQNode);
  });
});


describe('ParallelEQNode', () => {
  let audioContext: AudioContext;
  const initialFrequencies = {
    low: 200, mid: 1000, high: 6500 // Only provide necessary for 3-band
  };
  const initialFrequencies4Band = {
    low: 200, midLo: 350, midHi: 2500, high: 6500 // Only provide necessary for 4-band
  };

  beforeEach(() => {
    audioContext = new AudioContext();
    // Spying on private methods is generally done by casting to 'any' or by modifying the prototype.
    // The prototype modification was causing issues with 'mockClear', so we'll spy on the instance.
  });

  test('should initialize filters and gains in 3-band mode correctly', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies);
    // @ts-ignore
    expect(node.is4BandMode).toBe(false);
    // @ts-ignore
    expect(node.lowFilter).not.toBeNull();
    // @ts-ignore
    expect(node.lowBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.midFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.highFilter).not.toBeNull();
    // @ts-ignore
    expect(node.highBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.midLoFilter).toBeNull();
    // @ts-ignore
    expect(node.midLoBandGain).toBeNull();
    // @ts-ignore
    expect(node.midHiFilter).toBeNull();
    // @ts-ignore
    expect(node.midHiBandGain).toBeNull();
    // @ts-ignore
    expect(node.lowFilter!.type).toBe('lowshelf');
    // @ts-ignore
    expect(node.midFilter!.type).toBe('peaking');
    // @ts-ignore
    expect(node.highFilter!.type).toBe('highshelf');
  });

  test('should initialize filters and gains in 4-band mode correctly', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies4Band);
    // @ts-ignore
    expect(node.is4BandMode).toBe(true);
    // @ts-ignore
    expect(node.lowFilter).not.toBeNull();
    // @ts-ignore
    expect(node.lowBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.midLoFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midLoBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.midHiFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midHiBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.highFilter).not.toBeNull();
    // @ts-ignore
    expect(node.highBandGain).not.toBeNull();
    // @ts-ignore
    expect(node.midFilter).toBeNull(); 
    // @ts-ignore
    expect(node.midBandGain).toBeNull();
    // @ts-ignore
    expect(node.lowFilter!.type).toBe('lowshelf');
    // @ts-ignore
    expect(node.midLoFilter!.type).toBe('peaking');
    // @ts-ignore
    expect(node.midHiFilter!.type).toBe('peaking');
    // @ts-ignore
    expect(node.highFilter!.type).toBe('highshelf');
  });

  test('setEQ should apply gains correctly in 3-band mode (dB to linear conversion)', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies);
    const eqValues = { low: -6, mid: 0, high: 6 }; 
    node.setEQ(eqValues, false);
    // @ts-ignore
    expect(node.lowBandGain!.gain.value).toBeCloseTo(Math.pow(10, -6 / 20));
    // @ts-ignore
    expect(node.midBandGain!.gain.value).toBeCloseTo(1.0); 
    // @ts-ignore
    expect(node.highBandGain!.gain.value).toBeCloseTo(Math.pow(10, 6 / 20));
  });

  test('setEQ should apply gains with fullKill in 4-band mode (dB to linear conversion)', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies4Band);
    const eqValues = { low: -100, midLo: 3, midHi: -100, high: 0 }; 
    node.setEQ(eqValues, true); 
    // @ts-ignore
    expect(node.lowBandGain!.gain.value).toBeCloseTo(Math.pow(10, -100 / 20));
    // @ts-ignore
    expect(node.midLoBandGain!.gain.value).toBeCloseTo(Math.pow(10, 3 / 20));
    // @ts-ignore
    expect(node.midHiBandGain!.gain.value).toBeCloseTo(Math.pow(10, -100 / 20));
    // @ts-ignore
    expect(node.highBandGain!.gain.value).toBeCloseTo(1.0); 
  });

  test('setEQFrequencies should update filter frequencies', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies);
    const newFreq = { low: 180, mid: 900, high: 6000 }; // 3-band
    node.setEQFrequencies(newFreq);
    // @ts-ignore
    expect(node.lowFilter!.frequency.value).toBe(180);
    // @ts-ignore
    expect(node.midFilter!.frequency.value).toBe(900);
    // @ts-ignore
    expect(node.highFilter!.frequency.value).toBe(6000);
  });

  test('setMode should switch between 3-band and 4-band and re-run setupFiltersAndGains', () => {
    const node = new ParallelEQNode(audioContext as any, initialFrequencies); 
    const setupSpy = jest.spyOn(node as any, 'setupFiltersAndGains'); // Cast to any to spy on private method
    // @ts-ignore
    expect(node.midFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midLoFilter).toBeNull();
    setupSpy.mockClear();

    // Set 4-band frequencies before switching mode so setupFiltersAndGains uses them
    node.setEQFrequencies(initialFrequencies4Band); 
    node.setMode(true); // Switch to 4-band
    expect(setupSpy).toHaveBeenCalledTimes(1); // setupFiltersAndGains is called by setMode
    // @ts-ignore
    expect(node.midFilter).toBeNull();
    // @ts-ignore
    expect(node.midLoFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midHiFilter).not.toBeNull();
    setupSpy.mockClear();

    // Set 3-band frequencies before switching back
    node.setEQFrequencies(initialFrequencies); // initialFrequencies is 3-band for this suite
    node.setMode(false); // Switch back to 3-band
    expect(setupSpy).toHaveBeenCalledTimes(1);
    // @ts-ignore
    expect(node.midFilter).not.toBeNull();
    // @ts-ignore
    expect(node.midLoFilter).toBeNull();
  });
});
