module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  moduleNameMapper: {
    // If you have path aliases in tsconfig.json, map them here
    // Example: '^@/(.*)$': '<rootDir>/src/$1'
    // Ensure this matches your project's path alias configuration if any.
    // If no aliases, this might not be strictly needed or might need adjustment
    // for how <PERSON><PERSON> resolves modules based on your tsconfig.json's baseUrl.
    '^@/(.*)$': '<rootDir>/src/$1', 
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      // ts-jest specific options
      tsconfig: 'tsconfig.json', // or tsconfig.test.json if you have a specific one
    }],
  },
  // Automatically clear mock calls and instances between every test
  clearMocks: true,
};
